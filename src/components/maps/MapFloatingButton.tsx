"use client";

import React from "react";
import { useLanguage } from "@/contexts/LanguageContext";
import { Menu, ChevronLeft, ChevronRight } from "lucide-react";

interface MapFloatingButtonProps {
  isOpen: boolean;
  onClick: () => void;
  hasUpdates?: boolean;
}

const MapFloatingButton: React.FC<MapFloatingButtonProps> = ({
  isOpen,
  onClick,
  hasUpdates = false,
}) => {
  const { isRTL } = useLanguage();

  return (
    <button
      onClick={onClick}
      className={`
        fixed top-20 z-40 bg-white shadow-lg rounded-lg p-3 hover:shadow-xl transition-all duration-300 ease-in-out
        ${isRTL ? "right-4" : "left-4"}
        ${isOpen ? "opacity-75" : "opacity-100"}
        group
      `}
    >
      <div className="flex items-center gap-2">
        {isOpen ? (
          isRTL ? (
            <ChevronRight className="w-5 h-5 text-gray-700" />
          ) : (
            <ChevronLeft className="w-5 h-5 text-gray-700" />
          )
        ) : (
          <Menu className="w-5 h-5 text-gray-700" />
        )}

        {/* Notification dot */}
        {hasUpdates && !isOpen && (
          <div className="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full animate-pulse">
            <div className="absolute inset-0 w-3 h-3 bg-red-500 rounded-full animate-ping opacity-75"></div>
          </div>
        )}
      </div>

      {/* Tooltip */}
      <div
        className={`
        absolute top-1/2 transform -translate-y-1/2 bg-gray-900 text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 whitespace-nowrap
        ${isRTL ? "right-full mr-2" : "left-full ml-2"}
      `}
      >
        {isOpen
          ? isRTL
            ? "إخفاء اللوحة"
            : "Hide Panel"
          : isRTL
          ? "إظهار اللوحة"
          : "Show Panel"}
      </div>
    </button>
  );
};

export default MapFloatingButton;
